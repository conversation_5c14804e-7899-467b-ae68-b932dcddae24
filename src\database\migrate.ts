#!/usr/bin/env bun

import dotenv from 'dotenv';
import { runMigrations, db } from './connection';
import { logger } from '../utils/logger';

// Load environment variables
dotenv.config();

async function main() {
  try {
    logger.info('Starting database migrations...');
    await runMigrations();
    logger.info('Database migrations completed successfully');
  } catch (error) {
    logger.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await db.close();
  }
}

// Run migrations if this file is executed directly
if (import.meta.main) {
  main();
}
