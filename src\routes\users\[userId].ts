import { Response } from 'express';
import { param, body, validationResult } from 'express-validator';
import { db } from '../../database/connection';
import { AuthenticatedRequest, authorize, authorizeOwnership } from '../../middleware/auth';
import { CustomError } from '../../middleware/errorHandler';
import { logger } from '../../utils/logger';

// Middleware for GET request
const getMiddleware = [
  param('userId').isUUID().withMessage('Invalid user ID'),
  authorize(['user.read', 'profile.read']),
  authorizeOwnership('userId')
];

// Middleware for PUT request
const putMiddleware = [
  param('userId').isUUID().withMessage('Invalid user ID'),
  authorize(['user.update', 'profile.update']),
  authorizeOwnership('userId'),
  body('firstName')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('companyName')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Company name must be between 2 and 255 characters'),
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
];

export const GET = async (req: AuthenticatedRequest, res: Response) => {
  // Apply middleware
  await Promise.all(getMiddleware.map(middleware => 
    new Promise((resolve, reject) => {
      middleware(req, res, (error) => {
        if (error) reject(error);
        else resolve(undefined);
      });
    })
  ));

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.params;

    // Get user with permissions
    const userResult = await db.query(`
      SELECT 
        u.id, u.email, u.user_type, u.first_name, u.last_name, 
        u.company_name, u.phone, u.avatar_url, u.is_active, 
        u.is_verified, u.last_login, u.created_at,
        array_agg(DISTINCT p.name) FILTER (WHERE p.name IS NOT NULL) as permissions,
        COUNT(DISTINCT rt.id) FILTER (WHERE rt.revoked_at IS NULL AND rt.expires_at > CURRENT_TIMESTAMP) as active_sessions
      FROM users u
      LEFT JOIN active_user_permissions aup ON u.id = aup.user_id
      LEFT JOIN permissions p ON aup.permission_id = p.id
      LEFT JOIN refresh_tokens rt ON u.id = rt.user_id
      WHERE u.id = $1
      GROUP BY u.id, u.email, u.user_type, u.first_name, u.last_name, 
               u.company_name, u.phone, u.avatar_url, u.is_active, 
               u.is_verified, u.last_login, u.created_at
    `, [userId]);

    if (userResult.rows.length === 0) {
      throw new CustomError('User not found', 404);
    }

    const user = userResult.rows[0];

    // Check if user is active
    if (!user.is_active) {
      throw new CustomError('User account is deactivated', 404);
    }

    // Format user data
    const userData = {
      id: user.id,
      email: user.email,
      userType: user.user_type,
      name: user.user_type === 'company' 
        ? user.company_name 
        : `${user.first_name} ${user.last_name}`,
      firstName: user.first_name,
      lastName: user.last_name,
      companyName: user.company_name,
      phone: user.phone,
      avatarUrl: user.avatar_url,
      isActive: user.is_active,
      isVerified: user.is_verified,
      lastLogin: user.last_login,
      createdAt: user.created_at,
      permissions: user.permissions || [],
      activeSessions: parseInt(user.active_sessions) || 0
    };

    res.json({
      success: true,
      message: 'User retrieved successfully',
      data: {
        user: userData
      }
    });

  } catch (error) {
    logger.error('Get user error:', error);
    throw error;
  }
};

export const PUT = async (req: AuthenticatedRequest, res: Response) => {
  // Apply middleware
  await Promise.all(putMiddleware.map(middleware => 
    new Promise((resolve, reject) => {
      middleware(req, res, (error) => {
        if (error) reject(error);
        else resolve(undefined);
      });
    })
  ));

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.params;
    const { firstName, lastName, companyName, phone } = req.body;

    // Get current user data
    const currentUserResult = await db.query(`
      SELECT user_type, is_active FROM users WHERE id = $1
    `, [userId]);

    if (currentUserResult.rows.length === 0) {
      throw new CustomError('User not found', 404);
    }

    const currentUser = currentUserResult.rows[0];

    if (!currentUser.is_active) {
      throw new CustomError('User account is deactivated', 404);
    }

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    if (firstName !== undefined) {
      updateFields.push(`first_name = $${paramIndex}`);
      updateValues.push(firstName);
      paramIndex++;
    }

    if (lastName !== undefined) {
      updateFields.push(`last_name = $${paramIndex}`);
      updateValues.push(lastName);
      paramIndex++;
    }

    if (companyName !== undefined) {
      updateFields.push(`company_name = $${paramIndex}`);
      updateValues.push(companyName);
      paramIndex++;
    }

    if (phone !== undefined) {
      updateFields.push(`phone = $${paramIndex}`);
      updateValues.push(phone);
      paramIndex++;
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No fields to update'
      });
    }

    // Add updated_at and user ID
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    updateValues.push(userId);

    // Update user
    const updateResult = await db.query(`
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, email, user_type, first_name, last_name, company_name, phone, updated_at
    `, updateValues);

    const updatedUser = updateResult.rows[0];

    // Format response
    const userData = {
      id: updatedUser.id,
      email: updatedUser.email,
      userType: updatedUser.user_type,
      name: updatedUser.user_type === 'company' 
        ? updatedUser.company_name 
        : `${updatedUser.first_name} ${updatedUser.last_name}`,
      firstName: updatedUser.first_name,
      lastName: updatedUser.last_name,
      companyName: updatedUser.company_name,
      phone: updatedUser.phone,
      updatedAt: updatedUser.updated_at
    };

    logger.info(`User ${userId} updated by ${req.user?.id}`);

    res.json({
      success: true,
      message: 'User updated successfully',
      data: {
        user: userData
      }
    });

  } catch (error) {
    logger.error('Update user error:', error);
    throw error;
  }
};
