import { Application, Router } from 'express';
import { readdirSync, statSync } from 'fs';
import { join, extname, basename } from 'path';
import { logger } from './logger';

interface RouteModule {
  default?: Router;
  router?: Router;
  GET?: Function;
  POST?: Function;
  PUT?: Function;
  PATCH?: Function;
  DELETE?: Function;
  middleware?: Function[];
  excludeFromAuth?: boolean;
}

export const routeLoader = async (app: Application): Promise<void> => {
  const routesPath = join(process.cwd(), 'src', 'routes');
  
  try {
    await loadRoutesFromDirectory(app, routesPath, '/api');
    logger.info('All routes loaded successfully');
  } catch (error) {
    logger.error('Failed to load routes:', error);
    throw error;
  }
};

const loadRoutesFromDirectory = async (
  app: Application,
  dirPath: string,
  basePath: string
): Promise<void> => {
  try {
    const items = readdirSync(dirPath);

    for (const item of items) {
      const itemPath = join(dirPath, item);
      const stat = statSync(itemPath);

      if (stat.isDirectory()) {
        // Recursively load routes from subdirectories
        const newBasePath = `${basePath}/${item}`;
        await loadRoutesFromDirectory(app, itemPath, newBasePath);
      } else if (stat.isFile() && (extname(item) === '.ts' || extname(item) === '.js')) {
        await loadRouteFile(app, itemPath, basePath, item);
      }
    }
  } catch (error) {
    // Directory doesn't exist yet, which is fine
    if ((error as any).code !== 'ENOENT') {
      throw error;
    }
  }
};

const loadRouteFile = async (
  app: Application,
  filePath: string,
  basePath: string,
  fileName: string
): Promise<void> => {
  try {
    const routeModule: RouteModule = await import(filePath);
    const routeName = basename(fileName, extname(fileName));
    
    // Skip index files for path generation
    const routePath = routeName === 'index' ? basePath : `${basePath}/${routeName}`;

    // Handle different export patterns
    if (routeModule.default && typeof routeModule.default === 'function') {
      // Express Router export
      app.use(routePath, routeModule.default);
      logger.info(`Loaded router: ${routePath}`);
    } else if (routeModule.router) {
      // Named router export
      app.use(routePath, routeModule.router);
      logger.info(`Loaded router: ${routePath}`);
    } else {
      // HTTP method exports
      const router = Router();
      let hasRoutes = false;

      // Apply middleware if provided
      if (routeModule.middleware && Array.isArray(routeModule.middleware)) {
        router.use(...routeModule.middleware);
      }

      // Register HTTP method handlers
      const methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'] as const;
      
      for (const method of methods) {
        if (routeModule[method] && typeof routeModule[method] === 'function') {
          const handler = routeModule[method] as Function;
          
          switch (method) {
            case 'GET':
              router.get('/', handler);
              break;
            case 'POST':
              router.post('/', handler);
              break;
            case 'PUT':
              router.put('/', handler);
              break;
            case 'PATCH':
              router.patch('/', handler);
              break;
            case 'DELETE':
              router.delete('/', handler);
              break;
          }
          
          hasRoutes = true;
          logger.info(`Loaded ${method} handler: ${routePath}`);
        }
      }

      if (hasRoutes) {
        // Store route metadata for auth exclusion
        if (routeModule.excludeFromAuth) {
          (router as any).excludeFromAuth = true;
        }
        
        app.use(routePath, router);
      }
    }
  } catch (error) {
    logger.error(`Failed to load route file ${filePath}:`, error);
    throw error;
  }
};

// Utility function to create dynamic routes with parameters
export const createDynamicRoute = (pattern: string): string => {
  // Convert [param] to :param for Express
  return pattern.replace(/\[([^\]]+)\]/g, ':$1');
};

// Utility function to extract route metadata
export const getRouteMetadata = (router: Router): any => {
  return {
    excludeFromAuth: (router as any).excludeFromAuth || false,
  };
};
