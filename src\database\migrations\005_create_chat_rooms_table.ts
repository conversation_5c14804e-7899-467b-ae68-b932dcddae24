import { Database } from '../connection';

export const up = async (db: Database): Promise<void> => {
  await db.query(`
    CREATE TYPE room_type AS ENUM ('direct', 'group', 'public', 'company');
    
    CREATE TABLE chat_rooms (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(255),
      description TEXT,
      room_type room_type NOT NULL DEFAULT 'group',
      created_by UUID NOT NULL REFERENCES users(id),
      company_id UUID REFERENCES users(id), -- For company-specific rooms
      is_active BOOLEAN DEFAULT true,
      max_participants INTEGER DEFAULT 100,
      settings JSONB DEFAULT '{}',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      
      -- Constraints
      CONSTRAINT check_direct_room_name CHECK (
        room_type != 'direct' OR name IS NULL
      ),
      CONSTRAINT check_public_room_name CHECK (
        room_type = 'direct' OR name IS NOT NULL
      ),
      CONSTRAINT check_company_room CHECK (
        room_type != 'company' OR company_id IS NOT NULL
      )
    );

    -- Indexes
    CREATE INDEX idx_chat_rooms_created_by ON chat_rooms(created_by);
    CREATE INDEX idx_chat_rooms_company_id ON chat_rooms(company_id);
    CREATE INDEX idx_chat_rooms_room_type ON chat_rooms(room_type);
    CREATE INDEX idx_chat_rooms_is_active ON chat_rooms(is_active);
    CREATE INDEX idx_chat_rooms_created_at ON chat_rooms(created_at);

    -- Trigger to update updated_at
    CREATE TRIGGER update_chat_rooms_updated_at 
      BEFORE UPDATE ON chat_rooms 
      FOR EACH ROW 
      EXECUTE FUNCTION update_updated_at_column();

    -- Function to generate direct room name
    CREATE OR REPLACE FUNCTION generate_direct_room_name(user1_id UUID, user2_id UUID)
    RETURNS VARCHAR(255) AS $$
    BEGIN
      -- Create consistent naming for direct rooms
      IF user1_id < user2_id THEN
        RETURN 'direct_' || user1_id || '_' || user2_id;
      ELSE
        RETURN 'direct_' || user2_id || '_' || user1_id;
      END IF;
    END;
    $$ language 'plpgsql';

    -- Function to create or get direct room between two users
    CREATE OR REPLACE FUNCTION get_or_create_direct_room(user1_id UUID, user2_id UUID)
    RETURNS UUID AS $$
    DECLARE
      room_id UUID;
      room_name VARCHAR(255);
    BEGIN
      room_name := generate_direct_room_name(user1_id, user2_id);
      
      -- Try to find existing direct room
      SELECT id INTO room_id
      FROM chat_rooms cr
      JOIN chat_participants cp1 ON cr.id = cp1.room_id AND cp1.user_id = user1_id
      JOIN chat_participants cp2 ON cr.id = cp2.room_id AND cp2.user_id = user2_id
      WHERE cr.room_type = 'direct'
        AND cr.is_active = true;
      
      -- If not found, create new direct room
      IF room_id IS NULL THEN
        INSERT INTO chat_rooms (room_type, created_by, is_active)
        VALUES ('direct', user1_id, true)
        RETURNING id INTO room_id;
        
        -- Add both users as participants
        INSERT INTO chat_participants (room_id, user_id, role, joined_at)
        VALUES 
          (room_id, user1_id, 'member', CURRENT_TIMESTAMP),
          (room_id, user2_id, 'member', CURRENT_TIMESTAMP);
      END IF;
      
      RETURN room_id;
    END;
    $$ language 'plpgsql';
  `);
};

export const down = async (db: Database): Promise<void> => {
  await db.query(`
    DROP FUNCTION IF EXISTS get_or_create_direct_room(UUID, UUID);
    DROP FUNCTION IF EXISTS generate_direct_room_name(UUID, UUID);
    DROP TRIGGER IF EXISTS update_chat_rooms_updated_at ON chat_rooms;
    DROP TABLE IF EXISTS chat_rooms;
    DROP TYPE IF EXISTS room_type;
  `);
};
