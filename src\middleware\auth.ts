import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { db } from '../database/connection';
import { CustomError } from './errorHandler';
import { logger } from '../utils/logger';
import { getRouteMetadata } from '../utils/routeLoader';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    userType: 'worker' | 'company';
    permissions?: string[];
  };
}

// Main authentication middleware
export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Check if route is excluded from authentication
    const router = req.route?.router;
    if (router && getRouteMetadata(router).excludeFromAuth) {
      return next();
    }

    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new CustomError('Access token required', 401);
    }

    const token = authHeader.substring(7);

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

    // Get user from database with permissions
    const userResult = await db.query(`
      SELECT 
        u.id, u.email, u.user_type, u.is_active, u.is_verified,
        u.first_name, u.last_name, u.company_name,
        array_agg(p.name) FILTER (WHERE p.name IS NOT NULL) as permissions
      FROM users u
      LEFT JOIN active_user_permissions aup ON u.id = aup.user_id
      LEFT JOIN permissions p ON aup.permission_id = p.id
      WHERE u.id = $1
      GROUP BY u.id, u.email, u.user_type, u.is_active, u.is_verified,
               u.first_name, u.last_name, u.company_name
    `, [decoded.userId]);

    if (userResult.rows.length === 0) {
      throw new CustomError('User not found', 401);
    }

    const user = userResult.rows[0];

    // Check if user is active
    if (!user.is_active) {
      throw new CustomError('Account is deactivated', 401);
    }

    // Attach user info to request
    req.user = {
      id: user.id,
      email: user.email,
      userType: user.user_type,
      permissions: user.permissions || []
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      logger.warn('Invalid JWT token:', error.message);
      return next(new CustomError('Invalid token', 401));
    }
    
    if (error instanceof jwt.TokenExpiredError) {
      logger.warn('Expired JWT token');
      return next(new CustomError('Token expired', 401));
    }

    logger.error('Authentication error:', error);
    next(error);
  }
};

// Permission-based authorization middleware
export const authorize = (requiredPermissions: string | string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    const permissions = Array.isArray(requiredPermissions) 
      ? requiredPermissions 
      : [requiredPermissions];

    const userPermissions = req.user.permissions || [];

    // Check if user has admin access (bypasses all permission checks)
    if (userPermissions.includes('admin.full_access')) {
      return next();
    }

    // Check if user has any of the required permissions
    const hasPermission = permissions.some(permission => 
      userPermissions.includes(permission)
    );

    if (!hasPermission) {
      logger.warn(`User ${req.user.id} attempted to access resource requiring permissions: ${permissions.join(', ')}`);
      return next(new CustomError('Insufficient permissions', 403));
    }

    next();
  };
};

// User type-based authorization middleware
export const authorizeUserType = (allowedTypes: string | string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    const types = Array.isArray(allowedTypes) ? allowedTypes : [allowedTypes];

    if (!types.includes(req.user.userType)) {
      logger.warn(`User ${req.user.id} (${req.user.userType}) attempted to access resource restricted to: ${types.join(', ')}`);
      return next(new CustomError('Access denied for your user type', 403));
    }

    next();
  };
};

// Middleware to check if user owns the resource
export const authorizeOwnership = (userIdParam: string = 'userId') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    const resourceUserId = req.params[userIdParam] || req.body[userIdParam];

    // Admin users can access any resource
    if (req.user.permissions?.includes('admin.full_access')) {
      return next();
    }

    // Check if user is accessing their own resource
    if (req.user.id !== resourceUserId) {
      logger.warn(`User ${req.user.id} attempted to access resource owned by ${resourceUserId}`);
      return next(new CustomError('Access denied to this resource', 403));
    }

    next();
  };
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

    const userResult = await db.query(`
      SELECT 
        u.id, u.email, u.user_type, u.is_active,
        array_agg(p.name) FILTER (WHERE p.name IS NOT NULL) as permissions
      FROM users u
      LEFT JOIN active_user_permissions aup ON u.id = aup.user_id
      LEFT JOIN permissions p ON aup.permission_id = p.id
      WHERE u.id = $1 AND u.is_active = true
      GROUP BY u.id, u.email, u.user_type, u.is_active
    `, [decoded.userId]);

    if (userResult.rows.length > 0) {
      const user = userResult.rows[0];
      req.user = {
        id: user.id,
        email: user.email,
        userType: user.user_type,
        permissions: user.permissions || []
      };
    }

    next();
  } catch (error) {
    // Silently continue without authentication
    next();
  }
};
