import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { body, validationResult } from 'express-validator';
import { db } from '../../database/connection';
import { CustomError } from '../../middleware/errorHandler';
import { logger } from '../../utils/logger';
import { v4 as uuidv4 } from 'uuid';

// Validation middleware
export const middleware = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
];

export const POST = async (req: Request, res: Response) => {
  try {
    // Check validation results
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password, rememberMe = false } = req.body;

    // Find user by email
    const userResult = await db.query(`
      SELECT id, email, password_hash, user_type, first_name, last_name, 
             company_name, is_active, is_verified
      FROM users 
      WHERE email = $1
    `, [email]);

    if (userResult.rows.length === 0) {
      throw new CustomError('Invalid credentials', 401);
    }

    const user = userResult.rows[0];

    // Check if user is active
    if (!user.is_active) {
      throw new CustomError('Account is deactivated', 401);
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      throw new CustomError('Invalid credentials', 401);
    }

    // Generate JWT tokens
    const accessTokenExpiry = rememberMe ? '30d' : (process.env.JWT_EXPIRES_IN || '7d');
    const refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRES_IN || '30d';

    const accessToken = jwt.sign(
      { 
        userId: user.id, 
        email: user.email, 
        userType: user.user_type 
      },
      process.env.JWT_SECRET!,
      { expiresIn: accessTokenExpiry }
    );

    const refreshToken = jwt.sign(
      { userId: user.id, tokenId: uuidv4() },
      process.env.JWT_SECRET!,
      { expiresIn: refreshTokenExpiry }
    );

    // Store refresh token in database
    const refreshTokenHash = await bcrypt.hash(refreshToken, 10);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + (rememberMe ? 30 : 7));

    await db.query(`
      INSERT INTO refresh_tokens (user_id, token_hash, expires_at, device_info, ip_address)
      VALUES ($1, $2, $3, $4, $5)
    `, [
      user.id,
      refreshTokenHash,
      expiresAt,
      JSON.stringify({
        userAgent: req.get('User-Agent'),
        rememberMe
      }),
      req.ip
    ]);

    // Update last login
    await db.query(`
      UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1
    `, [user.id]);

    // Prepare user data for response
    const userData = {
      id: user.id,
      email: user.email,
      userType: user.user_type,
      name: user.user_type === 'company' 
        ? user.company_name 
        : `${user.first_name} ${user.last_name}`,
      isVerified: user.is_verified
    };

    logger.info(`User ${user.email} logged in successfully`);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userData,
        accessToken,
        refreshToken,
        expiresIn: accessTokenExpiry
      }
    });

  } catch (error) {
    logger.error('Login error:', error);
    throw error;
  }
};

// Exclude this route from authentication
export const excludeFromAuth = true;
