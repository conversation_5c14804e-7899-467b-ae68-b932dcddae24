import { Pool, PoolClient } from 'pg';
import { logger } from '../utils/logger';

class Database {
  private pool: Pool;
  private static instance: Database;

  private constructor() {
    this.pool = new Pool({
      user: process.env.POSTGRES_USER,
      host: process.env.POSTGRES_HOST,
      database: process.env.POSTGRES_DB,
      password: process.env.POSTGRES_PASSWORD,
      port: parseInt(process.env.POSTGRES_PORT || '5432'),
      max: 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
      connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
    });

    // Handle pool errors
    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  public async getClient(): Promise<PoolClient> {
    return this.pool.connect();
  }

  public async query(text: string, params?: any[]): Promise<any> {
    const client = await this.getClient();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  }

  public async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.getClient();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  public async testConnection(): Promise<boolean> {
    try {
      const result = await this.query('SELECT NOW()');
      logger.info('Database connection successful');
      return true;
    } catch (error) {
      logger.error('Database connection failed:', error);
      return false;
    }
  }

  public async close(): Promise<void> {
    await this.pool.end();
    logger.info('Database pool closed');
  }
}

export const db = Database.getInstance();

export const initializeDatabase = async (): Promise<void> => {
  const isConnected = await db.testConnection();
  if (!isConnected) {
    throw new Error('Failed to connect to database');
  }
  
  // Run migrations
  await runMigrations();
};

export const runMigrations = async (): Promise<void> => {
  try {
    // Create migrations table if it doesn't exist
    await db.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Get list of executed migrations
    const executedMigrations = await db.query('SELECT name FROM migrations ORDER BY id');
    const executedNames = executedMigrations.rows.map((row: any) => row.name);

    // Define migrations in order
    const migrations = [
      '001_create_users_table',
      '002_create_permissions_table',
      '003_create_user_permissions_table',
      '004_create_refresh_tokens_table',
      '005_create_chat_rooms_table',
      '006_create_chat_messages_table',
      '007_create_chat_participants_table',
    ];

    // Execute pending migrations
    for (const migrationName of migrations) {
      if (!executedNames.includes(migrationName)) {
        logger.info(`Running migration: ${migrationName}`);
        
        const migrationModule = await import(`./migrations/${migrationName}.js`);
        await migrationModule.up(db);
        
        await db.query('INSERT INTO migrations (name) VALUES ($1)', [migrationName]);
        logger.info(`Migration completed: ${migrationName}`);
      }
    }

    logger.info('All migrations completed successfully');
  } catch (error) {
    logger.error('Migration failed:', error);
    throw error;
  }
};
