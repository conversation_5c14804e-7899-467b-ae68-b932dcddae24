import { Database } from '../connection';

export const up = async (db: Database): Promise<void> => {
  await db.query(`
    CREATE TABLE permissions (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name VARCHAR(100) UNIQUE NOT NULL,
      description TEXT,
      resource VARCHAR(100) NOT NULL,
      action VARCHAR(50) NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      
      -- Ensure unique combination of resource and action
      UNIQUE(resource, action)
    );

    -- Indexes
    CREATE INDEX idx_permissions_name ON permissions(name);
    CREATE INDEX idx_permissions_resource ON permissions(resource);
    CREATE INDEX idx_permissions_action ON permissions(action);

    -- Insert default permissions
    INSERT INTO permissions (name, description, resource, action) VALUES
    -- User management permissions
    ('user.create', 'Create new users', 'user', 'create'),
    ('user.read', 'View user information', 'user', 'read'),
    ('user.update', 'Update user information', 'user', 'update'),
    ('user.delete', 'Delete users', 'user', 'delete'),
    ('user.list', 'List all users', 'user', 'list'),
    
    -- Profile management
    ('profile.read', 'View own profile', 'profile', 'read'),
    ('profile.update', 'Update own profile', 'profile', 'update'),
    
    -- Chat permissions
    ('chat.create_room', 'Create chat rooms', 'chat', 'create_room'),
    ('chat.join_room', 'Join chat rooms', 'chat', 'join_room'),
    ('chat.send_message', 'Send messages in chat', 'chat', 'send_message'),
    ('chat.read_messages', 'Read chat messages', 'chat', 'read_messages'),
    ('chat.delete_message', 'Delete chat messages', 'chat', 'delete_message'),
    ('chat.moderate', 'Moderate chat rooms', 'chat', 'moderate'),
    
    -- Company specific permissions
    ('company.manage_workers', 'Manage company workers', 'company', 'manage_workers'),
    ('company.view_analytics', 'View company analytics', 'company', 'view_analytics'),
    
    -- Worker specific permissions
    ('worker.apply_jobs', 'Apply for jobs', 'worker', 'apply_jobs'),
    ('worker.view_jobs', 'View available jobs', 'worker', 'view_jobs'),
    
    -- Admin permissions
    ('admin.full_access', 'Full administrative access', 'admin', 'full_access');
  `);
};

export const down = async (db: Database): Promise<void> => {
  await db.query(`
    DROP TABLE IF EXISTS permissions;
  `);
};
