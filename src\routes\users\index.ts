import { Response } from 'express';
import { query, validationResult } from 'express-validator';
import { db } from '../../database/connection';
import { AuthenticatedRequest, authorize } from '../../middleware/auth';
import { logger } from '../../utils/logger';

// Middleware for this route
export const middleware = [
  authorize('user.list'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('userType')
    .optional()
    .isIn(['worker', 'company'])
    .withMessage('User type must be worker or company'),
  query('search')
    .optional()
    .isLength({ min: 2 })
    .withMessage('Search term must be at least 2 characters'),
];

export const GET = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const userType = req.query.userType as string;
    const search = req.query.search as string;
    const offset = (page - 1) * limit;

    // Build query conditions
    let whereConditions = ['u.is_active = true'];
    let queryParams: any[] = [];
    let paramIndex = 1;

    if (userType) {
      whereConditions.push(`u.user_type = $${paramIndex}`);
      queryParams.push(userType);
      paramIndex++;
    }

    if (search) {
      whereConditions.push(`(
        u.email ILIKE $${paramIndex} OR 
        u.first_name ILIKE $${paramIndex} OR 
        u.last_name ILIKE $${paramIndex} OR 
        u.company_name ILIKE $${paramIndex}
      )`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';

    // Get total count
    const countResult = await db.query(`
      SELECT COUNT(*) as total
      FROM users u
      ${whereClause}
    `, queryParams);

    const total = parseInt(countResult.rows[0].total);

    // Get users with pagination
    const usersResult = await db.query(`
      SELECT 
        u.id, u.email, u.user_type, u.first_name, u.last_name, 
        u.company_name, u.phone, u.avatar_url, u.is_verified, 
        u.last_login, u.created_at,
        COUNT(DISTINCT rt.id) FILTER (WHERE rt.revoked_at IS NULL AND rt.expires_at > CURRENT_TIMESTAMP) as active_sessions
      FROM users u
      LEFT JOIN refresh_tokens rt ON u.id = rt.user_id
      ${whereClause}
      GROUP BY u.id, u.email, u.user_type, u.first_name, u.last_name, 
               u.company_name, u.phone, u.avatar_url, u.is_verified, 
               u.last_login, u.created_at
      ORDER BY u.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...queryParams, limit, offset]);

    // Format user data
    const users = usersResult.rows.map(user => ({
      id: user.id,
      email: user.email,
      userType: user.user_type,
      name: user.user_type === 'company' 
        ? user.company_name 
        : `${user.first_name} ${user.last_name}`,
      firstName: user.first_name,
      lastName: user.last_name,
      companyName: user.company_name,
      phone: user.phone,
      avatarUrl: user.avatar_url,
      isVerified: user.is_verified,
      lastLogin: user.last_login,
      createdAt: user.created_at,
      activeSessions: parseInt(user.active_sessions) || 0
    }));

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      message: 'Users retrieved successfully',
      data: {
        users,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    logger.error('Get users error:', error);
    throw error;
  }
};
