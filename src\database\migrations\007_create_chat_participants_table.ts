import { Database } from '../connection';

export const up = async (db: Database): Promise<void> => {
  await db.query(`
    CREATE TYPE participant_role AS ENUM ('owner', 'admin', 'moderator', 'member');
    
    CREATE TABLE chat_participants (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      room_id UUID NOT NULL REFERENCES chat_rooms(id) ON DELETE CASCADE,
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      role participant_role NOT NULL DEFAULT 'member',
      joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      left_at TIMESTAMP,
      invited_by UUID REFERENCES users(id),
      is_muted BOOLEAN DEFAULT false,
      muted_until TIMESTAMP,
      last_read_message_id UUID REFERENCES chat_messages(id),
      last_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      
      -- Ensure unique user-room combination for active participants
      UNIQUE(room_id, user_id)
    );

    -- Indexes
    CREATE INDEX idx_chat_participants_room_id ON chat_participants(room_id);
    CREATE INDEX idx_chat_participants_user_id ON chat_participants(user_id);
    CREATE INDEX idx_chat_participants_role ON chat_participants(role);
    CREATE INDEX idx_chat_participants_joined_at ON chat_participants(joined_at);
    CREATE INDEX idx_chat_participants_left_at ON chat_participants(left_at);
    CREATE INDEX idx_chat_participants_last_seen_at ON chat_participants(last_seen_at);

    -- View for active participants (not left)
    CREATE VIEW active_chat_participants AS
    SELECT *
    FROM chat_participants
    WHERE left_at IS NULL;

    -- Function to automatically add room creator as owner
    CREATE OR REPLACE FUNCTION add_room_creator_as_owner()
    RETURNS TRIGGER AS $$
    BEGIN
      -- Only add for non-direct rooms (direct rooms handle participants differently)
      IF NEW.room_type != 'direct' THEN
        INSERT INTO chat_participants (room_id, user_id, role, joined_at)
        VALUES (NEW.id, NEW.created_by, 'owner', CURRENT_TIMESTAMP);
      END IF;
      RETURN NEW;
    END;
    $$ language 'plpgsql';

    -- Trigger to add room creator as owner
    CREATE TRIGGER add_creator_as_owner
      AFTER INSERT ON chat_rooms
      FOR EACH ROW
      EXECUTE FUNCTION add_room_creator_as_owner();

    -- Function to check if user can perform action in room
    CREATE OR REPLACE FUNCTION can_user_perform_action(
      p_user_id UUID,
      p_room_id UUID,
      p_action VARCHAR(50)
    )
    RETURNS BOOLEAN AS $$
    DECLARE
      user_role participant_role;
      room_creator UUID;
    BEGIN
      -- Get user's role in the room
      SELECT role INTO user_role
      FROM active_chat_participants
      WHERE user_id = p_user_id AND room_id = p_room_id;
      
      -- If user is not a participant, they can't perform any actions
      IF user_role IS NULL THEN
        RETURN FALSE;
      END IF;
      
      -- Get room creator
      SELECT created_by INTO room_creator
      FROM chat_rooms
      WHERE id = p_room_id;
      
      -- Define action permissions
      CASE p_action
        WHEN 'send_message' THEN
          RETURN user_role IN ('owner', 'admin', 'moderator', 'member');
        WHEN 'delete_message' THEN
          RETURN user_role IN ('owner', 'admin', 'moderator');
        WHEN 'invite_user' THEN
          RETURN user_role IN ('owner', 'admin', 'moderator');
        WHEN 'remove_user' THEN
          RETURN user_role IN ('owner', 'admin');
        WHEN 'change_room_settings' THEN
          RETURN user_role IN ('owner', 'admin');
        WHEN 'delete_room' THEN
          RETURN user_role = 'owner' OR p_user_id = room_creator;
        ELSE
          RETURN FALSE;
      END CASE;
    END;
    $$ language 'plpgsql';

    -- Function to get unread message count for user in room
    CREATE OR REPLACE FUNCTION get_unread_count(p_user_id UUID, p_room_id UUID)
    RETURNS INTEGER AS $$
    DECLARE
      last_read_id UUID;
      unread_count INTEGER;
    BEGIN
      -- Get user's last read message
      SELECT last_read_message_id INTO last_read_id
      FROM chat_participants
      WHERE user_id = p_user_id AND room_id = p_room_id AND left_at IS NULL;
      
      -- If no last read message, count all messages
      IF last_read_id IS NULL THEN
        SELECT COUNT(*)::INTEGER INTO unread_count
        FROM chat_messages
        WHERE room_id = p_room_id 
          AND sender_id != p_user_id 
          AND deleted_at IS NULL;
      ELSE
        -- Count messages after last read
        SELECT COUNT(*)::INTEGER INTO unread_count
        FROM chat_messages
        WHERE room_id = p_room_id 
          AND sender_id != p_user_id 
          AND created_at > (
            SELECT created_at 
            FROM chat_messages 
            WHERE id = last_read_id
          )
          AND deleted_at IS NULL;
      END IF;
      
      RETURN COALESCE(unread_count, 0);
    END;
    $$ language 'plpgsql';
  `);
};

export const down = async (db: Database): Promise<void> => {
  await db.query(`
    DROP FUNCTION IF EXISTS get_unread_count(UUID, UUID);
    DROP FUNCTION IF EXISTS can_user_perform_action(UUID, UUID, VARCHAR);
    DROP TRIGGER IF EXISTS add_creator_as_owner ON chat_rooms;
    DROP FUNCTION IF EXISTS add_room_creator_as_owner();
    DROP VIEW IF EXISTS active_chat_participants;
    DROP TABLE IF EXISTS chat_participants;
    DROP TYPE IF EXISTS participant_role;
  `);
};
