import { Database } from '../connection';

export const up = async (db: Database): Promise<void> => {
  await db.query(`
    CREATE TYPE message_type AS ENUM ('text', 'image', 'file', 'system');
    
    CREATE TABLE chat_messages (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      room_id UUID NOT NULL REFERENCES chat_rooms(id) ON DELETE CASCADE,
      sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      message_type message_type NOT NULL DEFAULT 'text',
      content TEXT NOT NULL,
      metadata JSONB DEFAULT '{}', -- For file info, image dimensions, etc.
      reply_to UUID REFERENCES chat_messages(id),
      edited_at TIMESTAMP,
      deleted_at TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      
      -- Constraints
      CONSTRAINT check_content_not_empty CHECK (
        LENGTH(TRIM(content)) > 0 OR message_type != 'text'
      )
    );

    -- Indexes for performance
    CREATE INDEX idx_chat_messages_room_id ON chat_messages(room_id);
    CREATE INDEX idx_chat_messages_sender_id ON chat_messages(sender_id);
    CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
    CREATE INDEX idx_chat_messages_reply_to ON chat_messages(reply_to);
    CREATE INDEX idx_chat_messages_deleted_at ON chat_messages(deleted_at);
    
    -- Composite index for room messages ordered by time
    CREATE INDEX idx_chat_messages_room_time ON chat_messages(room_id, created_at DESC);

    -- Table for message reactions
    CREATE TABLE message_reactions (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      emoji VARCHAR(10) NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      
      -- Ensure unique user-message-emoji combination
      UNIQUE(message_id, user_id, emoji)
    );

    -- Indexes for reactions
    CREATE INDEX idx_message_reactions_message_id ON message_reactions(message_id);
    CREATE INDEX idx_message_reactions_user_id ON message_reactions(user_id);

    -- Table for message read status
    CREATE TABLE message_read_status (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      message_id UUID NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      
      -- Ensure unique user-message combination
      UNIQUE(message_id, user_id)
    );

    -- Indexes for read status
    CREATE INDEX idx_message_read_status_message_id ON message_read_status(message_id);
    CREATE INDEX idx_message_read_status_user_id ON message_read_status(user_id);
    CREATE INDEX idx_message_read_status_read_at ON message_read_status(read_at);

    -- View for messages with reaction counts
    CREATE VIEW messages_with_reactions AS
    SELECT 
      m.*,
      COALESCE(
        json_agg(
          json_build_object(
            'emoji', r.emoji,
            'count', r.reaction_count,
            'users', r.user_ids
          )
        ) FILTER (WHERE r.emoji IS NOT NULL), 
        '[]'::json
      ) as reactions
    FROM chat_messages m
    LEFT JOIN (
      SELECT 
        message_id,
        emoji,
        COUNT(*) as reaction_count,
        array_agg(user_id) as user_ids
      FROM message_reactions
      GROUP BY message_id, emoji
    ) r ON m.id = r.message_id
    WHERE m.deleted_at IS NULL
    GROUP BY m.id, m.room_id, m.sender_id, m.message_type, m.content, 
             m.metadata, m.reply_to, m.edited_at, m.deleted_at, m.created_at;

    -- Function to update room's last activity
    CREATE OR REPLACE FUNCTION update_room_last_activity()
    RETURNS TRIGGER AS $$
    BEGIN
      UPDATE chat_rooms 
      SET updated_at = CURRENT_TIMESTAMP 
      WHERE id = NEW.room_id;
      RETURN NEW;
    END;
    $$ language 'plpgsql';

    -- Trigger to update room activity on new message
    CREATE TRIGGER update_room_activity_on_message
      AFTER INSERT ON chat_messages
      FOR EACH ROW
      EXECUTE FUNCTION update_room_last_activity();
  `);
};

export const down = async (db: Database): Promise<void> => {
  await db.query(`
    DROP TRIGGER IF EXISTS update_room_activity_on_message ON chat_messages;
    DROP FUNCTION IF EXISTS update_room_last_activity();
    DROP VIEW IF EXISTS messages_with_reactions;
    DROP TABLE IF EXISTS message_read_status;
    DROP TABLE IF EXISTS message_reactions;
    DROP TABLE IF EXISTS chat_messages;
    DROP TYPE IF EXISTS message_type;
  `);
};
