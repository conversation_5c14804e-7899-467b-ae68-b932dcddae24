import { Database } from '../connection';

export const up = async (db: Database): Promise<void> => {
  await db.query(`
    CREATE TYPE user_type AS ENUM ('worker', 'company');
    
    CREATE TABLE users (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      email VARCHAR(255) UNIQUE NOT NULL,
      password_hash VARCHAR(255) NOT NULL,
      user_type user_type NOT NULL,
      first_name VA<PERSON><PERSON><PERSON>(100),
      last_name VA<PERSON><PERSON><PERSON>(100),
      company_name VA<PERSON>HAR(255),
      phone VARCHAR(20),
      avatar_url TEXT,
      is_active BOOLEAN DEFAULT true,
      is_verified BOOLEAN DEFAULT false,
      last_login TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      
      -- Constraints
      CONSTRAINT check_worker_names CHECK (
        user_type != 'worker' OR (first_name IS NOT NULL AND last_name IS NOT NULL)
      ),
      CONSTRAINT check_company_name CHECK (
        user_type != 'company' OR company_name IS NOT NULL
      )
    );

    -- Indexes for performance
    CREATE INDEX idx_users_email ON users(email);
    CREATE INDEX idx_users_user_type ON users(user_type);
    CREATE INDEX idx_users_is_active ON users(is_active);
    CREATE INDEX idx_users_created_at ON users(created_at);

    -- Function to update updated_at timestamp
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = CURRENT_TIMESTAMP;
      RETURN NEW;
    END;
    $$ language 'plpgsql';

    -- Trigger to automatically update updated_at
    CREATE TRIGGER update_users_updated_at 
      BEFORE UPDATE ON users 
      FOR EACH ROW 
      EXECUTE FUNCTION update_updated_at_column();
  `);
};

export const down = async (db: Database): Promise<void> => {
  await db.query(`
    DROP TRIGGER IF EXISTS update_users_updated_at ON users;
    DROP FUNCTION IF EXISTS update_updated_at_column();
    DROP TABLE IF EXISTS users;
    DROP TYPE IF EXISTS user_type;
  `);
};
