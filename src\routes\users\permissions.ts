import { Response } from 'express';
import { param, body, validationResult } from 'express-validator';
import { db } from '../../database/connection';
import { AuthenticatedRequest, authorize } from '../../middleware/auth';
import { CustomError } from '../../middleware/errorHandler';
import { logger } from '../../utils/logger';

// Middleware for GET request
const getMiddleware = [
  param('userId').isUUID().withMessage('Invalid user ID'),
  authorize('user.read')
];

// Middleware for POST request (grant permission)
const postMiddleware = [
  param('userId').isUUID().withMessage('Invalid user ID'),
  authorize('admin.full_access'), // Only admins can grant permissions
  body('permissionName')
    .notEmpty()
    .withMessage('Permission name is required'),
  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Expires at must be a valid date')
];

// Middleware for DELETE request (revoke permission)
const deleteMiddleware = [
  param('userId').isUUID().withMessage('Invalid user ID'),
  param('permissionId').isUUID().withMessage('Invalid permission ID'),
  authorize('admin.full_access') // Only admins can revoke permissions
];

export const GET = async (req: AuthenticatedRequest, res: Response) => {
  // Apply middleware
  await Promise.all(getMiddleware.map(middleware => 
    new Promise((resolve, reject) => {
      middleware(req, res, (error) => {
        if (error) reject(error);
        else resolve(undefined);
      });
    })
  ));

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.params;

    // Get user permissions
    const permissionsResult = await db.query(`
      SELECT 
        up.id as user_permission_id,
        p.id as permission_id,
        p.name,
        p.description,
        p.resource,
        p.action,
        up.granted_at,
        up.expires_at,
        granter.email as granted_by_email
      FROM user_permissions up
      JOIN permissions p ON up.permission_id = p.id
      LEFT JOIN users granter ON up.granted_by = granter.id
      WHERE up.user_id = $1
        AND (up.expires_at IS NULL OR up.expires_at > CURRENT_TIMESTAMP)
      ORDER BY p.resource, p.action
    `, [userId]);

    // Group permissions by resource
    const permissionsByResource: { [key: string]: any[] } = {};
    
    permissionsResult.rows.forEach(permission => {
      if (!permissionsByResource[permission.resource]) {
        permissionsByResource[permission.resource] = [];
      }
      
      permissionsByResource[permission.resource].push({
        userPermissionId: permission.user_permission_id,
        permissionId: permission.permission_id,
        name: permission.name,
        description: permission.description,
        action: permission.action,
        grantedAt: permission.granted_at,
        expiresAt: permission.expires_at,
        grantedByEmail: permission.granted_by_email
      });
    });

    res.json({
      success: true,
      message: 'User permissions retrieved successfully',
      data: {
        userId,
        permissions: permissionsByResource,
        totalPermissions: permissionsResult.rows.length
      }
    });

  } catch (error) {
    logger.error('Get user permissions error:', error);
    throw error;
  }
};

export const POST = async (req: AuthenticatedRequest, res: Response) => {
  // Apply middleware
  await Promise.all(postMiddleware.map(middleware => 
    new Promise((resolve, reject) => {
      middleware(req, res, (error) => {
        if (error) reject(error);
        else resolve(undefined);
      });
    })
  ));

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.params;
    const { permissionName, expiresAt } = req.body;

    // Check if user exists
    const userResult = await db.query(`
      SELECT id, email FROM users WHERE id = $1 AND is_active = true
    `, [userId]);

    if (userResult.rows.length === 0) {
      throw new CustomError('User not found', 404);
    }

    // Check if permission exists
    const permissionResult = await db.query(`
      SELECT id, name FROM permissions WHERE name = $1
    `, [permissionName]);

    if (permissionResult.rows.length === 0) {
      throw new CustomError('Permission not found', 404);
    }

    const permission = permissionResult.rows[0];

    // Check if user already has this permission
    const existingPermission = await db.query(`
      SELECT id FROM user_permissions 
      WHERE user_id = $1 AND permission_id = $2
        AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
    `, [userId, permission.id]);

    if (existingPermission.rows.length > 0) {
      throw new CustomError('User already has this permission', 409);
    }

    // Grant permission
    const grantResult = await db.query(`
      INSERT INTO user_permissions (user_id, permission_id, granted_by, expires_at)
      VALUES ($1, $2, $3, $4)
      RETURNING id, granted_at
    `, [userId, permission.id, req.user?.id, expiresAt || null]);

    logger.info(`Permission ${permissionName} granted to user ${userId} by ${req.user?.id}`);

    res.status(201).json({
      success: true,
      message: 'Permission granted successfully',
      data: {
        userPermissionId: grantResult.rows[0].id,
        userId,
        permissionName,
        grantedAt: grantResult.rows[0].granted_at,
        expiresAt: expiresAt || null
      }
    });

  } catch (error) {
    logger.error('Grant permission error:', error);
    throw error;
  }
};

export const DELETE = async (req: AuthenticatedRequest, res: Response) => {
  // Apply middleware
  await Promise.all(deleteMiddleware.map(middleware => 
    new Promise((resolve, reject) => {
      middleware(req, res, (error) => {
        if (error) reject(error);
        else resolve(undefined);
      });
    })
  ));

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId, permissionId } = req.params;

    // Check if user permission exists
    const userPermissionResult = await db.query(`
      SELECT up.id, p.name as permission_name
      FROM user_permissions up
      JOIN permissions p ON up.permission_id = p.id
      WHERE up.user_id = $1 AND up.permission_id = $2
        AND (up.expires_at IS NULL OR up.expires_at > CURRENT_TIMESTAMP)
    `, [userId, permissionId]);

    if (userPermissionResult.rows.length === 0) {
      throw new CustomError('User permission not found', 404);
    }

    const userPermission = userPermissionResult.rows[0];

    // Revoke permission (soft delete by setting expires_at to now)
    await db.query(`
      UPDATE user_permissions 
      SET expires_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [userPermission.id]);

    logger.info(`Permission ${userPermission.permission_name} revoked from user ${userId} by ${req.user?.id}`);

    res.json({
      success: true,
      message: 'Permission revoked successfully',
      data: {
        userId,
        permissionName: userPermission.permission_name,
        revokedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Revoke permission error:', error);
    throw error;
  }
};
