import { Request, Response } from 'express';

// This route will be available at /api
export const GET = async (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'CheffUp API v1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      chat: '/api/chat',
      health: '/health'
    }
  });
};

// Exclude this route from authentication
export const excludeFromAuth = true;
