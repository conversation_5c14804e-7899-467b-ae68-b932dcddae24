import { Response } from 'express';
import { param, body, query, validationResult } from 'express-validator';
import { db } from '../../../../database/connection';
import { AuthenticatedRequest, authorize } from '../../../../middleware/auth';
import { CustomError } from '../../../../middleware/errorHandler';
import { logger } from '../../../../utils/logger';

// Middleware for GET request (get messages)
const getMiddleware = [
  param('roomId').isUUID().withMessage('Invalid room ID'),
  authorize('chat.read_messages'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('before')
    .optional()
    .isISO8601()
    .withMessage('Before must be a valid date'),
];

// Middleware for POST request (send message)
const postMiddleware = [
  param('roomId').isUUID().withMessage('Invalid room ID'),
  authorize('chat.send_message'),
  body('content')
    .notEmpty()
    .isLength({ max: 4000 })
    .withMessage('Message content is required and must be less than 4000 characters'),
  body('messageType')
    .optional()
    .isIn(['text', 'image', 'file', 'system'])
    .withMessage('Invalid message type'),
  body('replyTo')
    .optional()
    .isUUID()
    .withMessage('Reply to must be a valid message ID'),
  body('metadata')
    .optional()
    .isObject()
    .withMessage('Metadata must be an object'),
];

export const GET = async (req: AuthenticatedRequest, res: Response) => {
  // Apply middleware
  await Promise.all(getMiddleware.map(middleware => 
    new Promise((resolve, reject) => {
      middleware(req, res, (error) => {
        if (error) reject(error);
        else resolve(undefined);
      });
    })
  ));

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { roomId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const before = req.query.before as string;

    // Check if user has access to this room
    const participantResult = await db.query(`
      SELECT cp.role, cr.name, cr.room_type
      FROM chat_participants cp
      JOIN chat_rooms cr ON cp.room_id = cr.id
      WHERE cp.room_id = $1 AND cp.user_id = $2 AND cp.left_at IS NULL
    `, [roomId, req.user?.id]);

    if (participantResult.rows.length === 0) {
      throw new CustomError('Access denied to this room', 403);
    }

    // Build query conditions
    let whereConditions = ['cm.room_id = $1', 'cm.deleted_at IS NULL'];
    let queryParams: any[] = [roomId];
    let paramIndex = 2;

    if (before) {
      whereConditions.push(`cm.created_at < $${paramIndex}`);
      queryParams.push(before);
      paramIndex++;
    }

    const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

    // Get messages with sender info and reactions
    const messagesResult = await db.query(`
      SELECT 
        cm.id, cm.content, cm.message_type, cm.metadata, cm.reply_to,
        cm.edited_at, cm.created_at,
        sender.id as sender_id,
        sender.first_name as sender_first_name,
        sender.last_name as sender_last_name,
        sender.company_name as sender_company_name,
        sender.user_type as sender_user_type,
        sender.avatar_url as sender_avatar,
        reply_msg.content as reply_content,
        reply_sender.first_name as reply_sender_first_name,
        reply_sender.last_name as reply_sender_last_name,
        reply_sender.company_name as reply_sender_company_name,
        reply_sender.user_type as reply_sender_user_type,
        COALESCE(
          json_agg(
            json_build_object(
              'emoji', mr.emoji,
              'count', reaction_counts.count,
              'users', reaction_counts.user_ids,
              'hasReacted', CASE WHEN user_reactions.user_id IS NOT NULL THEN true ELSE false END
            )
          ) FILTER (WHERE mr.emoji IS NOT NULL), 
          '[]'::json
        ) as reactions
      FROM chat_messages cm
      JOIN users sender ON cm.sender_id = sender.id
      LEFT JOIN chat_messages reply_msg ON cm.reply_to = reply_msg.id
      LEFT JOIN users reply_sender ON reply_msg.sender_id = reply_sender.id
      LEFT JOIN (
        SELECT message_id, emoji, COUNT(*) as count, array_agg(user_id) as user_ids
        FROM message_reactions
        GROUP BY message_id, emoji
      ) reaction_counts ON cm.id = reaction_counts.message_id
      LEFT JOIN message_reactions mr ON cm.id = mr.message_id AND mr.emoji = reaction_counts.emoji
      LEFT JOIN message_reactions user_reactions ON cm.id = user_reactions.message_id 
        AND user_reactions.user_id = $${paramIndex} AND user_reactions.emoji = mr.emoji
      ${whereClause}
      GROUP BY cm.id, cm.content, cm.message_type, cm.metadata, cm.reply_to,
               cm.edited_at, cm.created_at, sender.id, sender.first_name,
               sender.last_name, sender.company_name, sender.user_type, sender.avatar_url,
               reply_msg.content, reply_sender.first_name, reply_sender.last_name,
               reply_sender.company_name, reply_sender.user_type
      ORDER BY cm.created_at DESC
      LIMIT $${paramIndex + 1} OFFSET $${paramIndex + 2}
    `, [...queryParams, req.user?.id, limit, (page - 1) * limit]);

    // Format messages
    const messages = messagesResult.rows.map(msg => ({
      id: msg.id,
      content: msg.content,
      messageType: msg.message_type,
      metadata: msg.metadata,
      editedAt: msg.edited_at,
      createdAt: msg.created_at,
      sender: {
        id: msg.sender_id,
        name: msg.sender_user_type === 'company' 
          ? msg.sender_company_name 
          : `${msg.sender_first_name} ${msg.sender_last_name}`,
        userType: msg.sender_user_type,
        avatarUrl: msg.sender_avatar
      },
      replyTo: msg.reply_to ? {
        id: msg.reply_to,
        content: msg.reply_content,
        senderName: msg.reply_sender_user_type === 'company'
          ? msg.reply_sender_company_name
          : `${msg.reply_sender_first_name} ${msg.reply_sender_last_name}`
      } : null,
      reactions: msg.reactions
    }));

    // Update user's last read message
    if (messages.length > 0) {
      await db.query(`
        UPDATE chat_participants 
        SET last_read_message_id = $1, last_seen_at = CURRENT_TIMESTAMP
        WHERE room_id = $2 AND user_id = $3
      `, [messages[0].id, roomId, req.user?.id]);
    }

    res.json({
      success: true,
      message: 'Messages retrieved successfully',
      data: {
        roomId,
        messages,
        pagination: {
          page,
          limit,
          hasMore: messages.length === limit
        }
      }
    });

  } catch (error) {
    logger.error('Get messages error:', error);
    throw error;
  }
};

export const POST = async (req: AuthenticatedRequest, res: Response) => {
  // Apply middleware
  await Promise.all(postMiddleware.map(middleware => 
    new Promise((resolve, reject) => {
      middleware(req, res, (error) => {
        if (error) reject(error);
        else resolve(undefined);
      });
    })
  ));

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { roomId } = req.params;
    const { content, messageType = 'text', replyTo, metadata = {} } = req.body;

    // Check if user can send messages in this room
    const canSend = await db.query(`
      SELECT can_user_perform_action($1, $2, 'send_message') as can_send
    `, [req.user?.id, roomId]);

    if (!canSend.rows[0]?.can_send) {
      throw new CustomError('Permission denied to send messages in this room', 403);
    }

    // Validate reply message if provided
    if (replyTo) {
      const replyMessage = await db.query(`
        SELECT id FROM chat_messages 
        WHERE id = $1 AND room_id = $2 AND deleted_at IS NULL
      `, [replyTo, roomId]);

      if (replyMessage.rows.length === 0) {
        throw new CustomError('Reply message not found', 404);
      }
    }

    // Insert message
    const messageResult = await db.query(`
      INSERT INTO chat_messages (room_id, sender_id, message_type, content, metadata, reply_to)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, created_at
    `, [roomId, req.user?.id, messageType, content, metadata, replyTo]);

    const newMessage = messageResult.rows[0];

    // Get sender info for response
    const senderResult = await db.query(`
      SELECT first_name, last_name, company_name, user_type, avatar_url
      FROM users WHERE id = $1
    `, [req.user?.id]);

    const sender = senderResult.rows[0];

    const messageData = {
      id: newMessage.id,
      content,
      messageType,
      metadata,
      replyTo,
      createdAt: newMessage.created_at,
      sender: {
        id: req.user?.id,
        name: sender.user_type === 'company' 
          ? sender.company_name 
          : `${sender.first_name} ${sender.last_name}`,
        userType: sender.user_type,
        avatarUrl: sender.avatar_url
      },
      reactions: []
    };

    logger.info(`Message sent by ${req.user?.id} in room ${roomId}`);

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        message: messageData
      }
    });

  } catch (error) {
    logger.error('Send message error:', error);
    throw error;
  }
};
