import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { body, validationResult } from 'express-validator';
import { db } from '../../database/connection';
import { AuthenticatedRequest } from '../../middleware/auth';
import { logger } from '../../utils/logger';

// Validation middleware for refresh token
export const middleware = [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required'),
];

export const POST = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { refreshToken, logoutAll = false } = req.body;
    const userId = req.user?.id;

    if (logoutAll && userId) {
      // Revoke all refresh tokens for the user
      await db.query(`
        UPDATE refresh_tokens 
        SET revoked_at = CURRENT_TIMESTAMP 
        WHERE user_id = $1 AND revoked_at IS NULL
      `, [userId]);

      logger.info(`All sessions revoked for user ${userId}`);
    } else if (refreshToken) {
      // Find and revoke the specific refresh token
      const tokenResult = await db.query(`
        SELECT id, user_id, token_hash 
        FROM refresh_tokens 
        WHERE revoked_at IS NULL AND expires_at > CURRENT_TIMESTAMP
      `);

      // Check each token hash to find the matching one
      for (const tokenRow of tokenResult.rows) {
        const isMatch = await bcrypt.compare(refreshToken, tokenRow.token_hash);
        if (isMatch) {
          await db.query(`
            UPDATE refresh_tokens 
            SET revoked_at = CURRENT_TIMESTAMP 
            WHERE id = $1
          `, [tokenRow.id]);

          logger.info(`Refresh token revoked for user ${tokenRow.user_id}`);
          break;
        }
      }
    }

    res.json({
      success: true,
      message: logoutAll ? 'Logged out from all devices' : 'Logged out successfully'
    });

  } catch (error) {
    logger.error('Logout error:', error);
    throw error;
  }
};
