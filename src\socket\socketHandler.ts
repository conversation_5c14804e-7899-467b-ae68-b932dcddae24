import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';
import { db } from '../database/connection';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userType?: string;
}

export const setupSocketIO = (io: Server): void => {
  // Authentication middleware for Socket.IO
  io.use(async (socket: AuthenticatedSocket, next) => {
    try {
      const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return next(new Error('Authentication token required'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      
      // Verify user exists and is active
      const userResult = await db.query(
        'SELECT id, user_type, is_active FROM users WHERE id = $1',
        [decoded.userId]
      );

      if (userResult.rows.length === 0 || !userResult.rows[0].is_active) {
        return next(new Error('Invalid or inactive user'));
      }

      socket.userId = decoded.userId;
      socket.userType = userResult.rows[0].user_type;
      
      logger.info(`Socket authenticated for user: ${socket.userId}`);
      next();
    } catch (error) {
      logger.error('Socket authentication failed:', error);
      next(new Error('Authentication failed'));
    }
  });

  io.on('connection', (socket: AuthenticatedSocket) => {
    logger.info(`User ${socket.userId} connected via Socket.IO`);

    // Join user to their personal room for direct notifications
    socket.join(`user:${socket.userId}`);

    // Handle joining chat rooms
    socket.on('join_room', async (data: { roomId: string }) => {
      try {
        const { roomId } = data;

        // Verify user has access to this room
        const participantResult = await db.query(`
          SELECT cp.*, cr.name, cr.room_type
          FROM chat_participants cp
          JOIN chat_rooms cr ON cp.room_id = cr.id
          WHERE cp.room_id = $1 AND cp.user_id = $2 AND cp.left_at IS NULL
        `, [roomId, socket.userId]);

        if (participantResult.rows.length === 0) {
          socket.emit('error', { message: 'Access denied to this room' });
          return;
        }

        socket.join(`room:${roomId}`);
        
        // Update user's last seen time
        await db.query(`
          UPDATE chat_participants 
          SET last_seen_at = CURRENT_TIMESTAMP 
          WHERE room_id = $1 AND user_id = $2
        `, [roomId, socket.userId]);

        socket.emit('joined_room', { 
          roomId, 
          roomName: participantResult.rows[0].name,
          roomType: participantResult.rows[0].room_type
        });

        logger.info(`User ${socket.userId} joined room ${roomId}`);
      } catch (error) {
        logger.error('Error joining room:', error);
        socket.emit('error', { message: 'Failed to join room' });
      }
    });

    // Handle leaving chat rooms
    socket.on('leave_room', (data: { roomId: string }) => {
      const { roomId } = data;
      socket.leave(`room:${roomId}`);
      socket.emit('left_room', { roomId });
      logger.info(`User ${socket.userId} left room ${roomId}`);
    });

    // Handle sending messages
    socket.on('send_message', async (data: {
      roomId: string;
      content: string;
      messageType?: string;
      replyTo?: string;
      metadata?: any;
    }) => {
      try {
        const { roomId, content, messageType = 'text', replyTo, metadata = {} } = data;

        // Verify user can send messages in this room
        const canSend = await db.query(`
          SELECT can_user_perform_action($1, $2, 'send_message') as can_send
        `, [socket.userId, roomId]);

        if (!canSend.rows[0]?.can_send) {
          socket.emit('error', { message: 'Permission denied to send messages' });
          return;
        }

        // Insert message into database
        const messageResult = await db.query(`
          INSERT INTO chat_messages (room_id, sender_id, message_type, content, metadata, reply_to)
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING id, created_at
        `, [roomId, socket.userId, messageType, content, metadata, replyTo]);

        const messageId = messageResult.rows[0].id;
        const createdAt = messageResult.rows[0].created_at;

        // Get sender info
        const senderResult = await db.query(`
          SELECT first_name, last_name, company_name, user_type, avatar_url
          FROM users WHERE id = $1
        `, [socket.userId]);

        const sender = senderResult.rows[0];
        const senderName = sender.user_type === 'company' 
          ? sender.company_name 
          : `${sender.first_name} ${sender.last_name}`;

        const messageData = {
          id: messageId,
          roomId,
          senderId: socket.userId,
          senderName,
          senderAvatar: sender.avatar_url,
          messageType,
          content,
          metadata,
          replyTo,
          createdAt,
          reactions: []
        };

        // Broadcast message to all users in the room
        io.to(`room:${roomId}`).emit('new_message', messageData);

        logger.info(`Message sent by ${socket.userId} in room ${roomId}`);
      } catch (error) {
        logger.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle message reactions
    socket.on('react_to_message', async (data: {
      messageId: string;
      emoji: string;
    }) => {
      try {
        const { messageId, emoji } = data;

        // Toggle reaction (add if not exists, remove if exists)
        const existingReaction = await db.query(`
          SELECT id FROM message_reactions 
          WHERE message_id = $1 AND user_id = $2 AND emoji = $3
        `, [messageId, socket.userId, emoji]);

        if (existingReaction.rows.length > 0) {
          // Remove reaction
          await db.query(`
            DELETE FROM message_reactions 
            WHERE message_id = $1 AND user_id = $2 AND emoji = $3
          `, [messageId, socket.userId, emoji]);
        } else {
          // Add reaction
          await db.query(`
            INSERT INTO message_reactions (message_id, user_id, emoji)
            VALUES ($1, $2, $3)
          `, [messageId, socket.userId, emoji]);
        }

        // Get updated reaction counts
        const reactionsResult = await db.query(`
          SELECT emoji, COUNT(*) as count, array_agg(user_id) as user_ids
          FROM message_reactions
          WHERE message_id = $1
          GROUP BY emoji
        `, [messageId]);

        // Get room ID for broadcasting
        const messageResult = await db.query(`
          SELECT room_id FROM chat_messages WHERE id = $1
        `, [messageId]);

        const roomId = messageResult.rows[0]?.room_id;

        if (roomId) {
          io.to(`room:${roomId}`).emit('message_reaction_updated', {
            messageId,
            reactions: reactionsResult.rows
          });
        }

        logger.info(`Reaction ${emoji} toggled by ${socket.userId} on message ${messageId}`);
      } catch (error) {
        logger.error('Error handling message reaction:', error);
        socket.emit('error', { message: 'Failed to update reaction' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data: { roomId: string }) => {
      socket.to(`room:${data.roomId}`).emit('user_typing', {
        userId: socket.userId,
        roomId: data.roomId
      });
    });

    socket.on('typing_stop', (data: { roomId: string }) => {
      socket.to(`room:${data.roomId}`).emit('user_stopped_typing', {
        userId: socket.userId,
        roomId: data.roomId
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      logger.info(`User ${socket.userId} disconnected from Socket.IO`);
    });
  });

  logger.info('Socket.IO server initialized');
};
