import { Response } from 'express';
import { db } from '../../database/connection';
import { AuthenticatedRequest } from '../../middleware/auth';
import { logger } from '../../utils/logger';

export const GET = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Get detailed user information
    const userResult = await db.query(`
      SELECT 
        u.id, u.email, u.user_type, u.first_name, u.last_name, 
        u.company_name, u.phone, u.avatar_url, u.is_active, 
        u.is_verified, u.last_login, u.created_at,
        array_agg(DISTINCT p.name) FILTER (WHERE p.name IS NOT NULL) as permissions,
        COUNT(DISTINCT rt.id) FILTER (WHERE rt.revoked_at IS NULL AND rt.expires_at > CURRENT_TIMESTAMP) as active_sessions
      FROM users u
      LEFT JOIN active_user_permissions aup ON u.id = aup.user_id
      LEFT JOIN permissions p ON aup.permission_id = p.id
      LEFT JOIN refresh_tokens rt ON u.id = rt.user_id
      WHERE u.id = $1
      GROUP BY u.id, u.email, u.user_type, u.first_name, u.last_name, 
               u.company_name, u.phone, u.avatar_url, u.is_active, 
               u.is_verified, u.last_login, u.created_at
    `, [req.user.id]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = userResult.rows[0];

    // Prepare user data for response
    const userData = {
      id: user.id,
      email: user.email,
      userType: user.user_type,
      name: user.user_type === 'company' 
        ? user.company_name 
        : `${user.first_name} ${user.last_name}`,
      firstName: user.first_name,
      lastName: user.last_name,
      companyName: user.company_name,
      phone: user.phone,
      avatarUrl: user.avatar_url,
      isActive: user.is_active,
      isVerified: user.is_verified,
      lastLogin: user.last_login,
      createdAt: user.created_at,
      permissions: user.permissions || [],
      activeSessions: parseInt(user.active_sessions) || 0
    };

    res.json({
      success: true,
      message: 'User profile retrieved successfully',
      data: {
        user: userData
      }
    });

  } catch (error) {
    logger.error('Get user profile error:', error);
    throw error;
  }
};
