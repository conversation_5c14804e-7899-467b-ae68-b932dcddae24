import { Database } from '../connection';

export const up = async (db: Database): Promise<void> => {
  await db.query(`
    CREATE TABLE user_permissions (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
      granted_by UUID REFERENCES users(id),
      granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      expires_at TIMESTAMP,
      
      -- Ensure unique user-permission combination
      UNIQUE(user_id, permission_id)
    );

    -- Indexes for performance
    CREATE INDEX idx_user_permissions_user_id ON user_permissions(user_id);
    CREATE INDEX idx_user_permissions_permission_id ON user_permissions(permission_id);
    CREATE INDEX idx_user_permissions_expires_at ON user_permissions(expires_at);

    -- Create a view for active permissions (not expired)
    CREATE VIEW active_user_permissions AS
    SELECT 
      up.user_id,
      up.permission_id,
      p.name as permission_name,
      p.resource,
      p.action,
      up.granted_at,
      up.expires_at
    FROM user_permissions up
    JOIN permissions p ON up.permission_id = p.id
    WHERE up.expires_at IS NULL OR up.expires_at > CURRENT_TIMESTAMP;

    -- Function to assign default permissions based on user type
    CREATE OR REPLACE FUNCTION assign_default_permissions()
    RETURNS TRIGGER AS $$
    BEGIN
      -- Default permissions for workers
      IF NEW.user_type = 'worker' THEN
        INSERT INTO user_permissions (user_id, permission_id)
        SELECT NEW.id, p.id
        FROM permissions p
        WHERE p.name IN (
          'profile.read',
          'profile.update',
          'chat.join_room',
          'chat.send_message',
          'chat.read_messages',
          'worker.apply_jobs',
          'worker.view_jobs'
        );
      END IF;

      -- Default permissions for companies
      IF NEW.user_type = 'company' THEN
        INSERT INTO user_permissions (user_id, permission_id)
        SELECT NEW.id, p.id
        FROM permissions p
        WHERE p.name IN (
          'profile.read',
          'profile.update',
          'chat.create_room',
          'chat.join_room',
          'chat.send_message',
          'chat.read_messages',
          'chat.moderate',
          'company.manage_workers',
          'company.view_analytics'
        );
      END IF;

      RETURN NEW;
    END;
    $$ language 'plpgsql';

    -- Trigger to assign default permissions when user is created
    CREATE TRIGGER assign_user_default_permissions
      AFTER INSERT ON users
      FOR EACH ROW
      EXECUTE FUNCTION assign_default_permissions();
  `);
};

export const down = async (db: Database): Promise<void> => {
  await db.query(`
    DROP TRIGGER IF EXISTS assign_user_default_permissions ON users;
    DROP FUNCTION IF EXISTS assign_default_permissions();
    DROP VIEW IF EXISTS active_user_permissions;
    DROP TABLE IF EXISTS user_permissions;
  `);
};
