import { Response } from 'express';
import { body, query, validationResult } from 'express-validator';
import { db } from '../../database/connection';
import { AuthenticatedRequest, authorize } from '../../middleware/auth';
import { CustomError } from '../../middleware/errorHandler';
import { logger } from '../../utils/logger';

// Middleware for GET request (list rooms)
const getMiddleware = [
  authorize('chat.read_messages'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  query('roomType')
    .optional()
    .isIn(['direct', 'group', 'public', 'company'])
    .withMessage('Invalid room type'),
];

// Middleware for POST request (create room)
const postMiddleware = [
  authorize('chat.create_room'),
  body('name')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Room name must be between 2 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  body('roomType')
    .isIn(['group', 'public', 'company'])
    .withMessage('Room type must be group, public, or company'),
  body('maxParticipants')
    .optional()
    .isInt({ min: 2, max: 1000 })
    .withMessage('Max participants must be between 2 and 1000'),
];

export const GET = async (req: AuthenticatedRequest, res: Response) => {
  // Apply middleware
  await Promise.all(getMiddleware.map(middleware => 
    new Promise((resolve, reject) => {
      middleware(req, res, (error) => {
        if (error) reject(error);
        else resolve(undefined);
      });
    })
  ));

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const roomType = req.query.roomType as string;
    const offset = (page - 1) * limit;

    // Build query conditions
    let whereConditions = [
      'cr.is_active = true',
      'cp.user_id = $1',
      'cp.left_at IS NULL'
    ];
    let queryParams: any[] = [req.user?.id];
    let paramIndex = 2;

    if (roomType) {
      whereConditions.push(`cr.room_type = $${paramIndex}`);
      queryParams.push(roomType);
      paramIndex++;
    }

    const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

    // Get total count
    const countResult = await db.query(`
      SELECT COUNT(DISTINCT cr.id) as total
      FROM chat_rooms cr
      JOIN chat_participants cp ON cr.id = cp.room_id
      ${whereClause}
    `, queryParams);

    const total = parseInt(countResult.rows[0].total);

    // Get rooms with last message and unread count
    const roomsResult = await db.query(`
      SELECT DISTINCT
        cr.id, cr.name, cr.description, cr.room_type, cr.created_by,
        cr.max_participants, cr.created_at, cr.updated_at,
        creator.first_name as creator_first_name,
        creator.last_name as creator_last_name,
        creator.company_name as creator_company_name,
        creator.user_type as creator_user_type,
        (
          SELECT json_build_object(
            'id', cm.id,
            'content', cm.content,
            'messageType', cm.message_type,
            'senderId', cm.sender_id,
            'senderName', CASE 
              WHEN sender.user_type = 'company' THEN sender.company_name
              ELSE CONCAT(sender.first_name, ' ', sender.last_name)
            END,
            'createdAt', cm.created_at
          )
          FROM chat_messages cm
          JOIN users sender ON cm.sender_id = sender.id
          WHERE cm.room_id = cr.id AND cm.deleted_at IS NULL
          ORDER BY cm.created_at DESC
          LIMIT 1
        ) as last_message,
        get_unread_count($1, cr.id) as unread_count,
        (
          SELECT COUNT(*)
          FROM chat_participants cp2
          WHERE cp2.room_id = cr.id AND cp2.left_at IS NULL
        ) as participant_count
      FROM chat_rooms cr
      JOIN chat_participants cp ON cr.id = cp.room_id
      JOIN users creator ON cr.created_by = creator.id
      ${whereClause}
      ORDER BY cr.updated_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...queryParams, limit, offset]);

    const rooms = roomsResult.rows.map(room => ({
      id: room.id,
      name: room.name,
      description: room.description,
      roomType: room.room_type,
      createdBy: room.created_by,
      creatorName: room.creator_user_type === 'company' 
        ? room.creator_company_name 
        : `${room.creator_first_name} ${room.creator_last_name}`,
      maxParticipants: room.max_participants,
      participantCount: parseInt(room.participant_count),
      lastMessage: room.last_message,
      unreadCount: parseInt(room.unread_count) || 0,
      createdAt: room.created_at,
      updatedAt: room.updated_at
    }));

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      message: 'Chat rooms retrieved successfully',
      data: {
        rooms,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    logger.error('Get chat rooms error:', error);
    throw error;
  }
};

export const POST = async (req: AuthenticatedRequest, res: Response) => {
  // Apply middleware
  await Promise.all(postMiddleware.map(middleware => 
    new Promise((resolve, reject) => {
      middleware(req, res, (error) => {
        if (error) reject(error);
        else resolve(undefined);
      });
    })
  ));

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, description, roomType, maxParticipants = 100 } = req.body;

    // Additional validation
    if (roomType !== 'direct' && !name) {
      throw new CustomError('Room name is required for non-direct rooms', 400);
    }

    // For company rooms, ensure user is a company
    if (roomType === 'company' && req.user?.userType !== 'company') {
      throw new CustomError('Only companies can create company rooms', 403);
    }

    // Create room
    const roomResult = await db.query(`
      INSERT INTO chat_rooms (
        name, description, room_type, created_by, 
        company_id, max_participants, is_active
      )
      VALUES ($1, $2, $3, $4, $5, $6, true)
      RETURNING id, name, description, room_type, created_by, 
                max_participants, created_at
    `, [
      name,
      description || null,
      roomType,
      req.user?.id,
      roomType === 'company' ? req.user?.id : null,
      maxParticipants
    ]);

    const newRoom = roomResult.rows[0];

    logger.info(`Chat room created: ${newRoom.id} by user ${req.user?.id}`);

    res.status(201).json({
      success: true,
      message: 'Chat room created successfully',
      data: {
        room: {
          id: newRoom.id,
          name: newRoom.name,
          description: newRoom.description,
          roomType: newRoom.room_type,
          createdBy: newRoom.created_by,
          maxParticipants: newRoom.max_participants,
          createdAt: newRoom.created_at
        }
      }
    });

  } catch (error) {
    logger.error('Create chat room error:', error);
    throw error;
  }
};
