import { Database } from '../connection';

export const up = async (db: Database): Promise<void> => {
  await db.query(`
    CREATE TABLE refresh_tokens (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      token_hash VARCHAR(255) NOT NULL,
      expires_at TIMESTAMP NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      revoked_at TIMESTAMP,
      replaced_by UUID REFERENCES refresh_tokens(id),
      device_info JSONB,
      ip_address INET,
      
      -- Ensure token uniqueness
      UNIQUE(token_hash)
    );

    -- Indexes for performance
    CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
    CREATE INDEX idx_refresh_tokens_token_hash ON refresh_tokens(token_hash);
    CREATE INDEX idx_refresh_tokens_expires_at ON refresh_tokens(expires_at);
    CREATE INDEX idx_refresh_tokens_revoked_at ON refresh_tokens(revoked_at);

    -- Function to clean up expired tokens
    CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
    RETURNS void AS $$
    BEGIN
      DELETE FROM refresh_tokens 
      WHERE expires_at < CURRENT_TIMESTAMP 
        OR revoked_at IS NOT NULL;
    END;
    $$ language 'plpgsql';

    -- Create a view for active refresh tokens
    CREATE VIEW active_refresh_tokens AS
    SELECT *
    FROM refresh_tokens
    WHERE expires_at > CURRENT_TIMESTAMP 
      AND revoked_at IS NULL;
  `);
};

export const down = async (db: Database): Promise<void> => {
  await db.query(`
    DROP VIEW IF EXISTS active_refresh_tokens;
    DROP FUNCTION IF EXISTS cleanup_expired_tokens();
    DROP TABLE IF EXISTS refresh_tokens;
  `);
};
