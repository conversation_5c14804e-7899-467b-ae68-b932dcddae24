import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { body, validationResult } from 'express-validator';
import { db } from '../../database/connection';
import { CustomError } from '../../middleware/errorHandler';
import { logger } from '../../utils/logger';

// Validation middleware
export const middleware = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  body('userType')
    .isIn(['worker', 'company'])
    .withMessage('User type must be either worker or company'),
  body('firstName')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('companyName')
    .optional()
    .isLength({ min: 2, max: 255 })
    .withMessage('Company name must be between 2 and 255 characters'),
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
];

export const POST = async (req: Request, res: Response) => {
  try {
    // Check validation results
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { 
      email, 
      password, 
      userType, 
      firstName, 
      lastName, 
      companyName, 
      phone 
    } = req.body;

    // Additional validation based on user type
    if (userType === 'worker' && (!firstName || !lastName)) {
      throw new CustomError('First name and last name are required for workers', 400);
    }

    if (userType === 'company' && !companyName) {
      throw new CustomError('Company name is required for companies', 400);
    }

    // Check if user already exists
    const existingUser = await db.query(
      'SELECT id FROM users WHERE email = $1',
      [email]
    );

    if (existingUser.rows.length > 0) {
      throw new CustomError('User with this email already exists', 409);
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const userResult = await db.query(`
      INSERT INTO users (
        email, password_hash, user_type, first_name, last_name, 
        company_name, phone, is_active, is_verified
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, true, false)
      RETURNING id, email, user_type, first_name, last_name, company_name, created_at
    `, [
      email,
      passwordHash,
      userType,
      firstName || null,
      lastName || null,
      companyName || null,
      phone || null
    ]);

    const newUser = userResult.rows[0];

    // Prepare user data for response (exclude sensitive information)
    const userData = {
      id: newUser.id,
      email: newUser.email,
      userType: newUser.user_type,
      name: newUser.user_type === 'company' 
        ? newUser.company_name 
        : `${newUser.first_name} ${newUser.last_name}`,
      createdAt: newUser.created_at
    };

    logger.info(`New user registered: ${email} (${userType})`);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: userData
      }
    });

  } catch (error) {
    logger.error('Registration error:', error);
    throw error;
  }
};

// Exclude this route from authentication
export const excludeFromAuth = true;
