import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { body, validationResult } from 'express-validator';
import { db } from '../../database/connection';
import { CustomError } from '../../middleware/errorHandler';
import { logger } from '../../utils/logger';
import { v4 as uuidv4 } from 'uuid';

// Validation middleware
export const middleware = [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required'),
];

export const POST = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { refreshToken } = req.body;

    // Verify refresh token
    let decoded;
    try {
      decoded = jwt.verify(refreshToken, process.env.JWT_SECRET!) as any;
    } catch (error) {
      throw new CustomError('Invalid refresh token', 401);
    }

    // Find the refresh token in database
    const tokenResult = await db.query(`
      SELECT rt.id, rt.user_id, rt.token_hash, rt.expires_at, rt.revoked_at,
             u.email, u.user_type, u.is_active
      FROM refresh_tokens rt
      JOIN users u ON rt.user_id = u.id
      WHERE rt.user_id = $1 AND rt.revoked_at IS NULL AND rt.expires_at > CURRENT_TIMESTAMP
    `, [decoded.userId]);

    if (tokenResult.rows.length === 0) {
      throw new CustomError('Refresh token not found or expired', 401);
    }

    // Find the matching token by comparing hashes
    let matchingToken = null;
    for (const tokenRow of tokenResult.rows) {
      const isMatch = await bcrypt.compare(refreshToken, tokenRow.token_hash);
      if (isMatch) {
        matchingToken = tokenRow;
        break;
      }
    }

    if (!matchingToken) {
      throw new CustomError('Invalid refresh token', 401);
    }

    const user = matchingToken;

    // Check if user is still active
    if (!user.is_active) {
      throw new CustomError('Account is deactivated', 401);
    }

    // Generate new access token
    const accessToken = jwt.sign(
      { 
        userId: user.user_id, 
        email: user.email, 
        userType: user.user_type 
      },
      process.env.JWT_SECRET!,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    );

    // Generate new refresh token
    const newRefreshToken = jwt.sign(
      { userId: user.user_id, tokenId: uuidv4() },
      process.env.JWT_SECRET!,
      { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d' }
    );

    // Hash new refresh token
    const newRefreshTokenHash = await bcrypt.hash(newRefreshToken, 10);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // Use transaction to update tokens
    await db.transaction(async (client) => {
      // Revoke old refresh token
      await client.query(`
        UPDATE refresh_tokens 
        SET revoked_at = CURRENT_TIMESTAMP, replaced_by = $1
        WHERE id = $2
      `, [null, matchingToken.id]); // We'll update with new token ID after insert

      // Insert new refresh token
      const newTokenResult = await client.query(`
        INSERT INTO refresh_tokens (user_id, token_hash, expires_at, device_info, ip_address)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id
      `, [
        user.user_id,
        newRefreshTokenHash,
        expiresAt,
        JSON.stringify({
          userAgent: req.get('User-Agent'),
          refreshed: true
        }),
        req.ip
      ]);

      // Update the replaced_by field
      await client.query(`
        UPDATE refresh_tokens 
        SET replaced_by = $1 
        WHERE id = $2
      `, [newTokenResult.rows[0].id, matchingToken.id]);
    });

    logger.info(`Tokens refreshed for user ${user.email}`);

    res.json({
      success: true,
      message: 'Tokens refreshed successfully',
      data: {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn: process.env.JWT_EXPIRES_IN || '7d'
      }
    });

  } catch (error) {
    logger.error('Token refresh error:', error);
    throw error;
  }
};

// Exclude this route from authentication
export const excludeFromAuth = true;
